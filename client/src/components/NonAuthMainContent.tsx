import { useConnectModal } from "thirdweb/react";
import { useTheme } from "@/hooks/use-theme";
import NonAuthWelcomePage from "@/components/NonAuthWelcomePage";
import { client, wallets, chains } from "@/lib/thirdweb";

const NonAuthMainContent = () => {
  const { theme } = useTheme();
  const { connect, isConnecting } = useConnectModal();

  // Handle sign-in trigger
  const handleSignInTrigger = async (message?: string) => {
    try {
      // Don't store the message - just trigger sign-in
      // The user will need to re-enter their message after signing in

      // Open the connect modal
      await connect({
        client,
        wallets,
        chains,
        theme,
        connectModal: {
          title: "Sign In to Web3AI",
          size: "compact",
        },
      });
    } catch (error) {
      console.error("Failed to connect wallet:", error);
    }
  };

  return (
    <main className="flex-1 flex flex-col h-full overflow-hidden bg-background">
      {/* Centered container with 70% width */}
      <div className="w-[70%] mx-auto flex flex-col h-full py-4">
        <NonAuthWelcomePage
          onSignInTrigger={handleSignInTrigger}
          isConnecting={isConnecting}
        />
      </div>
    </main>
  );
};

export default NonAuthMainContent;
