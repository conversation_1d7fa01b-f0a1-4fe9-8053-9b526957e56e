import { useState, useRef, useEffect } from "react";
import { useLocation } from "wouter";

import { But<PERSON> } from "@/components/ui/button";
import { PlusIcon, MessageCircleIcon, ActivityIcon } from "lucide-react";

import { useChatStore } from "@/store/chatStore";
import BlockchainInfo from "@/components/BlockchainInfo";
import RecentActivity from "@/components/RecentActivity";
import { CubeIcon } from "@/components/icons";
import { useActiveAccount } from "thirdweb/react";
import { useNebulaChat } from "@/hooks/use-nebula-chat";

interface SidebarProps {
  isLoading?: boolean;
}

const Sidebar = ({ isLoading: propIsLoading = false }: SidebarProps) => {
  const { chats, activeChat, setActiveChat } = useChatStore();
  const account = useActiveAccount();
  const address = account?.address;
  const chatListRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<"chats" | "activity">("chats");

  // Get current location and navigation function from wouter
  const [location, navigate] = useLocation();

  // Use our custom Nebula chat hook
  const {
    chats: nebulaChats,
    activeChat: nebulaActiveChat,
    isLoading: nebulaIsLoading,
    setActiveChat: setNebulaActiveChat,
  } = useNebulaChat();

  // Combined loading state
  const isLoading = propIsLoading || nebulaIsLoading.createChat;

  // Handle new chat button click - navigate to home page and show fresh chat UI
  const handleNewChat = () => {
    // Navigate to home page if not already there
    if (location !== "/") {
      navigate("/");
    }

    if (address) {
      // For wallet-connected users, clear active chat to show fresh UI
      // Chat will be created when user sends first message
      setNebulaActiveChat(null);
    } else {
      // For non-wallet users, clear regular active chat to return to home state
      setActiveChat(null);
    }
  };

  // Select chat based on wallet connection status
  const handleSelectChat = (chat: any) => {
    if (address) {
      setNebulaActiveChat(chat);
    } else {
      setActiveChat(chat);
    }
  };

  // Combined chats and active chat based on wallet connection
  const displayChats = address ? nebulaChats : chats;
  const currentActiveChat = address ? nebulaActiveChat : activeChat;

  // Check if chat list is scrollable and add appropriate class
  useEffect(() => {
    const chatListElement = chatListRef.current;
    if (chatListElement) {
      const isScrollable =
        chatListElement.scrollHeight > chatListElement.clientHeight;
      if (isScrollable) {
        chatListElement.classList.add("scrollable");
      } else {
        chatListElement.classList.remove("scrollable");
      }
    }
  }, [displayChats]);

  return (
    <aside className="bg-sidebar-background flex flex-col w-full md:w-96 lg:w-80 xl:w-96 h-screen md:min-h-screen relative">
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent to-sidebar-accent/20 pointer-events-none"></div>
      <div className="relative z-10 flex flex-col h-full">
        <div className="p-3">
          <div className="flex items-center gap-3">
            <div className="nebula-icon-bg w-8 h-8 flex items-center justify-center">
              <CubeIcon className="text-sidebar-foreground text-lg" />
            </div>
            <h1 className="text-xl font-bold text-sidebar-foreground no-underline tracking-wide">
              Web3AI
            </h1>
          </div>
        </div>

        <div className="p-3">
          <Button
            className="w-full text-sidebar-foreground transition-all duration-200 hover:scale-[1.02] hover:shadow-lg hover:shadow-primary/20 active:scale-[0.98] group"
            style={{
              border: "1px solid hsl(320deg 75.21% 40.62% / 50%)",
              background:
                "linear-gradient(135deg, hsl(320deg 6.51% 20.98% / 40%), hsl(var(--primary) / 0.2))",
            }}
            onMouseEnter={(e) => {
              if (!address) return;
              e.currentTarget.style.background =
                "linear-gradient(135deg, hsl(320deg 6.51% 25.98% / 60%), hsl(var(--primary) / 0.35))";
              e.currentTarget.style.borderColor =
                "hsl(320deg 75.21% 50.62% / 70%)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background =
                "linear-gradient(135deg, hsl(320deg 6.51% 20.98% / 40%), hsl(var(--primary) / 0.2))";
              e.currentTarget.style.borderColor =
                "hsl(320deg 75.21% 40.62% / 50%)";
            }}
            onClick={handleNewChat}
            disabled={!address}
          >
            <PlusIcon className="h-4 w-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
            {address ? "New Chat" : "Connect Wallet to Chat"}
          </Button>
        </div>

        {/* Tabbed Interface */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Tab Headers */}
          <div className="px-3 py-2 flex-shrink-0">
            <div className="flex space-x-1 bg-muted/30 rounded-md p-1">
              <button
                onClick={() => setActiveTab("chats")}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-sm transition-colors ${
                  activeTab === "chats"
                    ? "bg-background text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                <MessageCircleIcon className="h-4 w-4" />
                Chat History
              </button>
              <button
                onClick={() => setActiveTab("activity")}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-sm transition-colors ${
                  activeTab === "activity"
                    ? "bg-background text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                <ActivityIcon className="h-4 w-4" />
                Recent Activity
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden">
            {activeTab === "chats" ? (
              <div className="h-full flex flex-col">
                <div
                  ref={chatListRef}
                  className="flex-1 overflow-y-auto px-3 sidebar-chat-list"
                >
                  <div className="space-y-1 pb-4">
                    {isLoading ? (
                      <div className="px-2 py-2 text-sm text-muted-foreground">
                        Loading chats...
                      </div>
                    ) : !address ? (
                      <div className="px-2 py-2 text-sm text-muted-foreground">
                        Connect your wallet to view chat history
                      </div>
                    ) : displayChats.length === 0 ? (
                      <div className="px-2 py-2 text-sm text-muted-foreground">
                        No recent chats
                      </div>
                    ) : (
                      displayChats.map((chat) => (
                        <button
                          key={chat.id}
                          className={`px-3 py-2 rounded-md w-full text-left flex items-center transition-colors ${
                            currentActiveChat?.id === chat.id
                              ? "bg-accent/50"
                              : "hover:bg-background/80"
                          }`}
                          onClick={() => handleSelectChat(chat)}
                          title={chat.title} // Show full title on hover
                        >
                          <MessageCircleIcon className="h-4 w-4 text-muted-foreground mr-3 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <span className="text-sm text-foreground truncate block">
                              {chat.title}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(chat.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </button>
                      ))
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-full overflow-y-auto px-3 sidebar-chat-list">
                <RecentActivity />
              </div>
            )}
          </div>
        </div>

        {/* Bottom Section - Fixed */}
        <div className="mt-auto">
          {/* BlockchainInfo Component in Sidebar */}
          <div className="px-3 pt-3 pb-3">
            <BlockchainInfo />
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
