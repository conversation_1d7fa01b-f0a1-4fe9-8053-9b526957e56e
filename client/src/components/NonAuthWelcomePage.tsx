import { Bot } from "lucide-react";
import NonAuthMessageInput from "@/components/NonAuthMessageInput";
import NonAuthSuggestionChips from "@/components/NonAuthSuggestionChips";

interface NonAuthWelcomePageProps {
  onSignInTrigger: (message?: string) => void;
  isConnecting?: boolean;
}

const NonAuthWelcomePage = ({ onSignInTrigger, isConnecting = false }: NonAuthWelcomePageProps) => {
  return (
    <div className="flex flex-col justify-center items-center h-full">
      <div className="text-center max-w-2xl px-4">
        <div
          className="w-20 h-20 mx-auto mb-8 flex items-center justify-center rounded-xl"
          style={{
            background:
              "linear-gradient(135deg, hsl(0deg 0% 3.92%), hsl(var(--primary) / 0.3))",
          }}
        >
          <Bot className="h-10 w-10 text-foreground" />
        </div>
        <h1 className="text-4xl font-bold mb-12 text-foreground">
          How can I help you
          <br />
          onchain today?
        </h1>

        {/* Query Input for Initial View */}
        <div className="w-full max-w-2xl mx-auto mb-6">
          <NonAuthMessageInput
            onSignInTrigger={onSignInTrigger}
            isConnecting={isConnecting}
          />
        </div>

        {/* Suggestion Chips */}
        <NonAuthSuggestionChips onSignInTrigger={onSignInTrigger} />
      </div>
    </div>
  );
};

export default NonAuthWelcomePage;
