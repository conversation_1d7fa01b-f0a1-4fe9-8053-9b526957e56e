import { useEffect } from "react";
import Sidebar from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import TopWalletButton from "@/components/TopWalletButton";
import { useChatStore } from "@/store/chatStore";
import { But<PERSON> } from "@/components/ui/button";
import { Sun, Moon } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { useNebulaChat } from "@/hooks/use-nebula-chat";

const LoggedInHome = () => {
  const { activeChat, setActiveChat, setChats } = useChatStore();
  const { theme, toggleTheme } = useTheme();

  // Get chat data from the Nebula chat hook
  const { chats, isLoading: isLoadingChats } = useNebulaChat();

  // Set chats in store when data is loaded
  useEffect(() => {
    if (chats && chats.length > 0) {
      setChats(chats);

      // Set first chat as active if no active chat
      if (!activeChat) {
        setActiveChat(chats[0]);
      }
    }
  }, [chats, activeChat, setChats, setActiveChat]);

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Signed-in users: Show theme toggle and wallet button in top right */}
      <header className="fixed top-0 right-0 z-50 p-4">
        <div className="flex items-center gap-3">
          {/* Theme toggle button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="h-9 w-9 p-0 nebula-hover"
            title={
              theme === "dark"
                ? "Switch to light mode"
                : "Switch to dark mode"
            }
          >
            {theme === "dark" ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>
          <TopWalletButton />
        </div>
      </header>

      {/* Main Layout */}
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Show sidebar for logged-in users */}
        <Sidebar isLoading={isLoadingChats} />
        <MainContent />
      </div>
    </div>
  );
};

export default LoggedInHome;
