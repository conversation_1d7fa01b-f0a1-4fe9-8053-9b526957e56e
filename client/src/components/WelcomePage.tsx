import { Bot } from "lucide-react";
import MessageInput from "@/components/MessageInput";
import SuggestionChips from "@/components/SuggestionChips";

interface WelcomePageProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
}

const WelcomePage = ({ onSendMessage, isLoading = false }: WelcomePageProps) => {
  return (
    <div className="flex flex-col justify-center items-center h-full">
      <div className="text-center max-w-2xl px-4">
        <div
          className="w-20 h-20 mx-auto mb-8 flex items-center justify-center rounded-xl"
          style={{
            background:
              "linear-gradient(135deg, hsl(0deg 0% 3.92%), hsl(var(--primary) / 0.3))",
          }}
        >
          <Bot className="h-10 w-10 text-foreground" />
        </div>
        <h1 className="text-4xl font-bold mb-12 text-foreground">
          How can I help you
          <br />
          onchain today?
        </h1>

        {/* Query Input for Initial View */}
        <div className="w-full max-w-2xl mx-auto mb-6">
          <MessageInput
            onSendMessage={onSendMessage}
            isLoading={isLoading}
            compact={false}
          />
        </div>

        {/* Suggestion Chips */}
        <SuggestionChips onChipClick={onSendMessage} />
      </div>
    </div>
  );
};

export default WelcomePage;
