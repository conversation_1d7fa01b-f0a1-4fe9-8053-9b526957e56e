import { Bot } from "lucide-react";
import MessageInput from "@/components/MessageInput";
import SuggestionChips from "@/components/SuggestionChips";

interface WelcomePageProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
}

const WelcomePage = ({
  onSendMessage,
  isLoading = false,
}: WelcomePageProps) => {
  return (
    <div className="flex flex-col justify-center items-center h-full min-h-[calc(100vh-8rem)]">
      <div className="text-center max-w-2xl px-4 sm:px-6 lg:px-8 w-full">
        <div
          className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-6 sm:mb-8 flex items-center justify-center rounded-xl"
          style={{
            background:
              "linear-gradient(135deg, hsl(0deg 0% 3.92%), hsl(var(--primary) / 0.3))",
          }}
        >
          <Bot className="h-8 w-8 sm:h-10 sm:w-10 text-foreground" />
        </div>
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-8 sm:mb-12 text-foreground leading-tight">
          How can I help you
          <br className="hidden sm:block" />
          <span className="sm:hidden"> </span>
          onchain today?
        </h1>

        {/* Query Input for Initial View */}
        <div className="w-full max-w-2xl mx-auto mb-4 sm:mb-6">
          <MessageInput
            onSendMessage={onSendMessage}
            isLoading={isLoading}
            compact={false}
          />
        </div>

        {/* Suggestion Chips */}
        <SuggestionChips onChipClick={onSendMessage} />
      </div>
    </div>
  );
};

export default WelcomePage;
