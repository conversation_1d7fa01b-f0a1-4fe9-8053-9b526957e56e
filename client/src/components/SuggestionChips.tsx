import {
  ArrowRightIcon,
  HelpCircleIcon,
  CodeIcon,
  DollarSignIcon,
  SearchIcon,
  SendIcon,
} from "lucide-react";

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void;
}

const suggestions = [
  "What can Nebula do?",
  "Launch a Token",
  "Buy USDC",
  "Analyze the Uniswap contracts",
  "Send ETH to someone",
];

const SuggestionChips = ({ onChipClick }: SuggestionChipsProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-4 px-2">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="nebula-suggestion-chip nebula-hover text-sm sm:text-base min-h-[44px] px-3 sm:px-4 py-2 sm:py-3"
          onClick={() => onChipClick(suggestion)}
        >
          <span className="truncate">{suggestion}</span>
          <ArrowRightIcon className="h-3 w-3 sm:h-4 sm:w-4 text-primary ml-1 sm:ml-2 flex-shrink-0" />
        </button>
      ))}
    </div>
  );
};

export default SuggestionChips;
