import { ArrowRightIcon } from "lucide-react";

interface NonAuthSuggestionChipsProps {
  onSignInTrigger: (suggestion: string) => void;
}

const suggestions = [
  "What can Nebula do?",
  "Launch a Token",
  "Buy USDC",
  "Analyze the Uniswap contracts",
  "Send ETH to someone",
];

const NonAuthSuggestionChips = ({ onSignInTrigger }: NonAuthSuggestionChipsProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 mb-4">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="nebula-suggestion-chip nebula-hover"
          onClick={() => onSignInTrigger(suggestion)}
          title="Sign in to use this suggestion"
        >
          {suggestion}
          <ArrowRightIcon className="h-3 w-3 text-primary ml-1" />
        </button>
      ))}
    </div>
  );
};

export default NonAuthSuggestionChips;
