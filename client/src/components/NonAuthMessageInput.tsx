import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUpIcon } from "lucide-react";

interface NonAuthMessageInputProps {
  onSignInTrigger: (message: string) => void;
  isConnecting?: boolean;
}

const NonAuthMessageInput = ({
  onSignInTrigger,
  isConnecting = false,
}: NonAuthMessageInputProps) => {
  const [message, setMessage] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSend = () => {
    if (!message.trim() || isConnecting) return;
    onSignInTrigger(message.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      {/* Container that looks like a single textarea */}
      <div
        className="relative bg-muted/30 theme-input rounded-md border outline-none focus:outline-none focus-visible:outline-none ring-1 ring-gray-200/20 dark:ring-gray-700/20"
        tabIndex={0}
        onFocus={() => {
          setIsFocused(true);
          textareaRef.current?.focus();
        }}
        onBlur={() => setIsFocused(false)}
        onClick={() => textareaRef.current?.focus()}
      >
        {/* Actual textarea for text input */}
        <Textarea
          ref={textareaRef}
          rows={1}
          placeholder="Ask Web3AI"
          className="w-full bg-transparent border-0 py-3 pl-4 pr-12 sm:pr-16 text-sm sm:text-base resize-none min-h-[50px] sm:min-h-[60px] max-h-[120px] overflow-y-auto outline-none focus:outline-none focus-visible:outline-none focus:ring-0 focus:border-0"
          style={{
            scrollbarWidth: "thin",
            scrollbarColor: "rgba(255, 255, 255, 0.1) transparent",
            outline: "none !important",
            border: "none !important",
            boxShadow: "none !important",
          }}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={isConnecting}
        />

        {/* Send button */}
        <Button
          className="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 text-primary bg-transparent hover:bg-transparent p-1 sm:p-0 focus:outline-none focus-visible:outline-none min-h-[44px] min-w-[44px] sm:min-h-auto sm:min-w-auto"
          onClick={handleSend}
          disabled={isConnecting || !message.trim()}
          title="Sign in to send message"
        >
          {isConnecting ? (
            <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
          ) : (
            <ArrowUpIcon className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Helper text */}
      <div className="text-xs sm:text-sm text-muted-foreground mt-2 text-center px-2">
        {isConnecting
          ? "Connecting wallet..."
          : "Connect your wallet to start chatting with Nebula"}
      </div>
    </div>
  );
};

export default NonAuthMessageInput;
