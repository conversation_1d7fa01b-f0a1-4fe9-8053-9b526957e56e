{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.tellaw/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,UklGRhoFAABXRUJQVlA4IA4FAABwHACdASqAAIAAPm0ylEckIyIhLBapAIANiWgA1qXvfqv4ga9/1PzVKa/TPwbwMNAeczyr/u/u799P+K9mX5c9gD9Tf8x1FfMB+un7c+0z6nP8t6gH9d/0XWAegZ5Zv7s/B//af936VX//aXW9RIf1ubsSqgXu1/G4/4apKlig3i8F/TkpXJQPULtXvBAeFEZww2Uy07noYtJzbuDJwJEJ/pjp0nzGin8qyWAUmE8fQ5Fh/MR1PF0mrtzfuIBt+MG37eWfq2p4mo75Vd9Z+pH0vEUtFGWi8tM5GviSNfkOK19SNm/P/vyG6bhQ8AD+/PhAbjm5CaPa6jLc3cryKjZI0gAtNqSwBncj7zGKKJFxWXrcKknuhFcA3LvpdRvhxFyW+AtYhqD6oK3JH46TvSJGXQHA0qtfCGgCVAc+jdPFAgO6yNvoA4DZCJmGh/TrMxfSefxn5pwazEbzlBLSJx9lHcTYQW7ev+zsY4zV4mgAxuk6hste66HulbEIWLpyx3qdsOLF9tSp9bomw73Ekrrwgi2NEVE4WJ+pB+6hqWMzBbhN/VsqARE2CByYtClS4DUFHAx1O+pZudYtSNrjPfbJl7PuCeFJHSvaFLU3Y0Oj2L4cOqo5/D/c66+jfsF873kXW0cJptXtZMt0Qwot/Tb/khK9umaGyq9bvjfeK53JVfxZxsFtndT99c04AlxUkEIUbMVnwibPadV2IzUBq43wbHO11oj/8w/ejecOvM2rf7jGFU7i1r9+94abCgbpp+J2WFvCG+21aYlrjTiUhoC5FXUAI1Vqtb+JHKFAyku2yW065torhyL7/+GIYQ5ExfXfksVtWGLDeB0OtBWzDBC+8Yf3mcHZIR1Y7Kts4DpBN8lkF9Hkf4OjQ468q+09CLtg+HYR+hNrakSKcnUypBKeh1WwOr/+RS8iPjpSSCAEwjQ2WFzE2eGVb+PvMXQ3ICt/fRPvqUGagfgZceyYeP7YLsb1C9fj0FEVg3aRE+Q2zX2QvOL/SiI1usvmKdtvSuM2mXv7Z6kHLv/LfmvvPlK4UZtJsILL12mK0Jbdmc8dJsv5x767PJy0V8W7IKdVb5TuWOLU9onMbNGaKMDNuU0TwMvgBLb4ZI1gaavtURv8na+GKV34ceNdGQdZABZg9qn9FGR3IvW9TGRlbVaHd2sJ7+MCczz6RXTDTXZLmCqpL6LB1PrAmhK3RrbO7hJv5X+rK+urOX0G7lvXfw+xd09NJuOYvTBfJ3tX3RCMwYyYP/xcbpLSrvlrGjuMiG3mog3g8JBpo7RQamt2BxY6hSys83T5rDHJQVZP3vyKD52w/Yywna1lDhKjsKDB1TfRUBAeaNjw6XKrv59z1fq4A+lQxNblKe8OsyfFzbp8y5H05dP4GJQoKy29FtrZhkK6eroH6oBfxxQF5zJlqoLnV4VYtWZGp/3W1AxWDf1edgbCnADHST/qQaU01fIIRliHSMJUuqBqVqY9aOt9IXUc0Mwiz8ULY3oXA4iNU3avVC/mKZl0j+84U/qAQSUC+iLWb94lgFwTaEVnX+xOFaezy0gH0RJTdPFFNOJ3Zb3AHvI0YPKJbre9SehL1R+i0lGPrGP79an3k7mM6yl45n7t/BturGnUamS6BBLI843KCtw//BvEa6srh4GBMiUyzZQxmhzW8kiM7TdSJAo4YwrQjDwJ4aVxh77FYrSQLWw3Nb3nd+sNg9Brrdfcuq4AAAAA\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}