{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.nonbank/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.nonbank\",\n  name: \"NonBank\",\n  homepage: \"https://nonbank.io/\",\n  image_id: \"fe06c7ed-3df1-4cc7-9686-c920914abd00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/nonbank-defi-crypto-wallet-app/id6477441479\",\n    android: \"https://play.google.com/store/apps/details?id=io.nonbank\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"nonbank://\",\n    universal: \"https://id.nonbank.io/wc\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}