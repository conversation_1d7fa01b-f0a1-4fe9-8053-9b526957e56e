{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.legionnetwork/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.legionnetwork\",\n  name: \"LegionNetwork\",\n  homepage: \"https://www.legionnetwork.io/\",\n  image_id: \"26044229-4a61-4b14-a2ed-5413fe435a00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/gb/app/legion-network-crypto-superapp/id1602921528\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.legion.production&pcampaignid=web_share\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"legionapp://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}