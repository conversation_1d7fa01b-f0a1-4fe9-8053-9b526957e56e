{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.helixid/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.helixid\",\n  name: \"helix id\",\n  homepage: \"https://helixid.io/\",\n  image_id: \"4083ef71-8389-4682-ded6-0099236d2e00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/de/app/helix-id/id1469238013?l=en\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.io.helix.id&hl=en&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"helix-id://helix-id.com\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}