import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.ethos/index.js
var wallet = {
  id: "io.ethos",
  name: "Ethos Self-Custody Vault",
  homepage: "https://www.ethos.io/",
  image_id: "8bc7fb62-6f6b-4473-2e4a-5691a646fc00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/ethos-self-custody-vault/id6450948705",
    android: "https://play.google.com/store/apps/details?id=com.ethos2.prod&hl=en_US&gl=US&pli=1",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "myapp://home",
    universal: null
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-GCZVWG6U.js.map
