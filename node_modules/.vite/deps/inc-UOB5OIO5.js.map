{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/inc.tomo/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"inc.tomo\",\n  name: \"Tomo Wallet\",\n  homepage: \"https://tomo.inc\",\n  image_id: \"95c9f957-b1e4-4619-fa7c-a77569d2fe00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/tomo-inc/id6468010287\",\n    android: \"https://play.google.com/store/apps/details?id=tomo.app.unyx\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chromewebstore.google.com/detail/tomo-wallet/pfccjkejcgoppjnllalolplgogenfojk\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: \"inc.tomo\",\n  mobile: {\n    native: \"tomo://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}