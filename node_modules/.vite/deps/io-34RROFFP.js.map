{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.getclave/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.getclave\",\n  name: \"<PERSON><PERSON><PERSON>\",\n  homepage: \"https://getclave.io\",\n  image_id: \"f1c538df-15d9-4448-542f-b7b358e95d00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/gr/app/clave-smart-wallet/id6449253761\",\n    android: \"https://play.google.com/store/apps/details?id=com.clave.mobile\",\n    mac: \"https://apps.apple.com/gr/app/clave-smart-wallet/id6449253761\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"clave://link/wc/\",\n    universal: \"https://getclave.io/link/wc\",\n  },\n  desktop: {\n    native: \"clave://link/wc/\",\n    universal: \"https://getclave.io/link\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}