{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.brave.wallet/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,UklGRtALAABXRUJQVlA4WAoAAAAQAAAAfwAAfwAAQUxQSJsDAAABoAVJtmnbGpt38/jatm3b99m2bV/btm3btm1722s+v7fH6PV5IyJiAuiRq7W69urxj93/Y4+/9vqlKjYD1Vjm9/6z5z96/9Y3rwIZcBmo1GxNJTs+qTAZYFG+CRGV/ODQLIKdOjCgOHq6OlBZv/cojYGm7n9kxmR67YHSFEdN3XoGkqHtLcX3ahNE1S4pzifLojDaU7Lnz5cjzW0vdFxjpe3ObXen5sqfL7vbbpRkqPrz2jNXr17cM/vH7Zrira38cc6es1cun1j1XQVB2Yc8VAgf9HNLyb4sojCG57llpK+IKpShBS4J6csjCmdwjoOfa25YIQ1MzsYtbVpYYQ2MtvFKHRVVaIN9nZxSe0QUXv8PbkY5NyrEy7Izcn0J6T0HIyp9H9DVwsTZPgnQUDsrc5swnEA9EytyHdfAaLusxDvlCJrELgcvcysFN17byMo5DI/q6mCV+xygQ+mcrO0V4oYmRukjIfVOZVTsEqRjBfjYn1eYO1rYZPYE9V0qG9dHoF6zszHUiUEKViK++Y9A2pGTUfo4SP1SGdlegfS4lZGhchSQpyRxztgHaJublXMEoN4OVtbn8WjtLawMpXxwbucn3rY9CTCJDTZmlmExMNGuZmbGJ6NoWhqZGYrcBXMlF3G3bI9Dia8ysaO+ISjBH4l/ex+UB00F5L0M5XSWAFofAxJZQBJ7+4F4vhHR/g6QGw1F5DkD5FiaCFochRGeSTJ/eQDjzmdCWl6Eca6ukOyHYOx0C6EZURDhCST169sgrn8gptlJEMdqisnaBGKlQwyNjUEIDia5n9yAcOkNQQ0PQthdRVDmMghzHYJoUAxAsDtJfusqgPMviqq/E8DmqqKyzwEwxS6KeiTERX4k2S9cEnf6SWHV1olbWlaYa4S4vhZh9I5f2M0XSHrdLcJWVRVn7y7se5M4an1a1N6GJD+tm6ivHACo2malCdHUinKE0NTpgpJ6upURAtleuSbkwrNWAml566yIEy+aCOeze/lpuzoR1OqL7jO7Pbs8gXX+do6TduobK+FtvS3Ixr+pPkFOH3KTydWeLkLdYT+LXc0JeL5BnqTd65mDoBva70vS1maE3lCsbyQJ/q6FSAdTOu753za1dJEumiv0Dv8vnp9LG0kvs3fZ9j+saZdBelqtv+8/POxVkXQ2x0sb/9Xq5zJIf2sPu/UP1wZWJV1Oe3utXynlXfGKk/S68m8rd6z4uRzpec6yWfSIRwBWUDggDggAANAkAJ0BKoAAgAA+bTCTRiQjIaEslmsQgA2JbAhwBfAH4AI0qVepykp0z8nfZUq79c/D/5N8w+bn1f94P3n9y/Jn4AerLzAP07/2P9t6xv9N/3vqA/nX91/Zn3hvQj/ZfUA/t//E9af/Xewr+4XsJ/rp6Zf7R/BL+4/7s/AV+vv/59gD0AOF8/tM4hv9yx+D+kamqeRzvVWL7FTgneNa7vO6Q7rntn5QV6qHFx2WdzQ3jRpJHfP7LcJ5m90kfTGielPYZmOF799DwEVXBfcpURiX1Cm7b7t7OSQtyueNJ3P6vM7V1ctfMlffAyOMbM8vnwYAJpfkyYyldher37lIhKIAoWMhh2Zxqrtvn2EYHVfYVBl1PX5/DzurpRx46s+lOQJXhyfGY0VL3tiqWIzuAAD+6i1S3S/AmlTCSemVfzqby/pQF0hOhZzI3P/1Kt7dj96ddj/4o21Wf5pR+0n2t3/mlH7SjK9yUF92CzFs8dukH6oVoRH9WjFkQN1gM9KqPpwtn8AsrfV8P//qNb//eVta5VR5+wvNlCEfPEBjtoO3VRQ77LYFJuteeOxjHwBnROiGNtk+jwMHqkClEckGOBrBOHX1JqcqBd6vDzec5VtAWOYokRu2G7Iq0bmdQ7+0Ypto2ucAfsa71M3VUmDxCjjJrMJEGRUKpVHTObP3bnu30+6SKFtO7+D9dDPLBPBS4OFfXfX9mn/8rh4YxoRqCw2p3UtdSswh+tl/HGj7GqRdv+ov6Q1wycUAQd5/WD4ZOdalIEA+TSKYxfKdyizuyuN92aw773khUf/53JPaX13lDCezPPPl8yYr/cbtu/+x/f/7Q2jJ4IPo4ZOIw94Jd9h7ziwk3/3rdgJZq1mJiKGCpuBGPcmPIqCBOCMpkCF1FO3zva+cOffe2PlvQBzhy4wtfAoyXjrLBBiahCKQjiaqb9ORHsooYloOD3T7mOQqm4Zdm+8biPdD1bedNerwG+**********************************ff4VI/h/oc3VHc8APMum74ItecE3lRE1x2BSJh0EruDFOdHqwzw6AxqteHBsRqn/N+PIb7fmpkPvacVkf/+LkWL6VRynU+9uB9aUnc/GOiZxGCwELKq0aFHSkz+RnYhTv/fzbe+wx1K2YpCfQhalWlf9O6vFIK9QL3acP6VX0rCbO+Oh80SNbNuO/WM2h84nyvHzRoJ/Yd8bZ2RF5eeRoQ17+yufrKDlA/BwF/qwnW973UnloTeYEWxdTwh1mRipFpRlsDQv76Zvtjy0NYegN0b/qr2BHJnJJGVr36jQEqlRTs5UNzD+kJ3nJsmKSzKSljFgoTYdlef8sFlYo3Wz0w9Zex3puwN41csM5VdnrxSrgxHwzf7SXccrqdTxSNBXbKRv3KZLhUNnm0+MAIy5Lu05QqzuVM43GX3M3kxuUEMfbG7eseF8cQkGgL6gvTxGyGiHuXXNmWnAa0DxtwJpmrrCdch0X9EfZxL/BCB62ZNoxl4BXVQ2zmYL61VMHrpqzRH5YtfOzGHcerm1de/uWhx2B7/r8cJ87ghAOk/cCXRZAJLCLbKfA9g21+77oMmaXKtY4ZAFl27MQ54dPXPH2kN/y7FSywB7UkQY99aLAI2ya4VIdL2AFIfdaIbkudwOCFJ7/xH8rmII8mo9ljzK03eSsSq4sxJ/c/679j1PU/kzzmrsZIi/c3+trwCm25S+3hwQSO/XVsqyJ+JPOoNH/WYbv+kFau5F/fm0Y5h8MtRKpSwQl2XCTxifGU9MTDGG+/8nk3D2OqD3/gCHAuZuDXBmMfaSUgCTqKe7Xeo3sGUXqPmZ6GhL2zLwL/0w9DLnukRqD8TX/Cl7mRca6fz/xxEoNrnml/MZZUZE/IPfSf7HlBc9gwwy4bCUq6Sb1519G+8ELPyo57KiAeU0DvR7lYyGJZatnbPD4bncy86ioM5fsaXdoyYJfUne3GYCnOAeQdscQbbf/+m23+oq57KpVbJnpgt2BOixSfUxRccvEjcqlGk/mppuY3Aondo0SyswMfn3rIYkL3i5zqHjUdLai3kPwm48cqbWUx83EPbZLE2CkU8hSlVD5OpgUKh9C3UGBouDQMjLX+H1oPfng3Ag8XiQoED1ZTGfKGSGj28hOuKadVN0pCVyR0+qxRhSxZPS2ZuDmwsf/C7/8guiEd59cIqzJKAofFsODgdcCovQ7nRGjc9f8i//dHImxA+k3F/ISw2BMIl98PiJ0vfdfH489aDEFWXadYjC/kca4XudoOQ09rWWKbyPl36BQu4oZW+TmgkdPF8uW45KsUYmfP3sgXHVWy1SWOgcZGj87KW5StFDK9NVS+hfOsaagJB1/MbCxeSL51uFRDCdgSvOwpZTYUY9G1Euy5CYiJZC8sxkwLwcoHo3P/B8oY49iE74+4crHmGmexralo+oe9OAl18dj5u7gXUQRyu8Rbcjaylnwry1vq0vDznbRegxMk4BMYnaBCHCUytajy2rQaGLF22KT65uqmucXjRE3hxwavYT+big6y6/XwDFqB8biIMWXtmuDwnUdmVWJaSde00J+pjoLch3IZKILuAFPYp28GI4TceIU/siQvzNZRvsgPI7POvd3I+7/WlULxo87OvM8wO9bI6En1NS0ssdEEFaPIvpkvjySMovlGTzKPNVjGLdlIhQABZeecwIljQ+xkIg9Z7XVxGtrnIt441TBj2Zz5XCd86TyfMXCn6oebaLXGfi09pkBRuAAAA=\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}