import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.pitaka/index.js
var wallet = {
  id: "io.pitaka",
  name: "Pit<PERSON>",
  homepage: "https://pitaka.io",
  image_id: "c816aeae-e0d1-4c52-f37a-efde6df1ee00",
  app: {
    browser: null,
    ios: "https://apps.apple.com/ph/app/pitaka-blockchain-wallet/id1644341925",
    android: "https://play.google.com/store/apps/details?id=com.pitakamobile",
    mac: null,
    windows: null,
    linux: null,
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "pitaka://",
    universal: "https://app.pitaka.io"
  },
  desktop: {
    native: null,
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-4N6PK75J.js.map
