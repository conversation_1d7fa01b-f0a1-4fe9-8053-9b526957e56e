{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.moonstake/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.moonstake\",\n  name: \"MO<PERSON><PERSON><PERSON><PERSON>\",\n  homepage: \"https://moonstake.io\",\n  image_id: \"22374fae-244c-4224-2e3d-c14912f98a00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/moonstake-wallet/id1502532651\",\n    android:\n      \"https://play.google.com/store/apps/details?id=io.moonstake.wallet&hl=en\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"moonstake://\",\n    universal: \"https://moonstake.io/launchApp\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}