{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.metamask/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.metamask\",\n  name: \"Meta<PERSON><PERSON>\",\n  homepage: \"https://metamask.io/\",\n  image_id: \"e30d09fe-c0dd-4b61-81e2-d6dc09eb9700\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/metamask/id1438144202\",\n    android: \"https://play.google.com/store/apps/details?id=io.metamask\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn\",\n    firefox: \"https://addons.mozilla.org/en-US/firefox/addon/ether-metamask/\",\n    safari: null,\n    edge: \"https://microsoftedge.microsoft.com/addons/detail/metamask/ejbal<PERSON>koplchl<PERSON><PERSON><PERSON><PERSON>eajnimhm?hl=en-US\",\n    opera: \"https://addons.opera.com/en-gb/extensions/details/metamask-10/\",\n  },\n  rdns: \"io.metamask\",\n  mobile: {\n    native: \"metamask://\",\n    universal: \"https://metamask.app.link\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n  deepLink: {\n    mobile: \"https://metamask.app.link/dapp/\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;EAEb,UAAU;IACR,QAAQ;;;", "names": []}