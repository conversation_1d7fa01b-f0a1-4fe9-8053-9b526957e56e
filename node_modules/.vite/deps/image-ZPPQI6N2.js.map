{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.coincircle/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,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\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}