{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/com.crossmint/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,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\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}