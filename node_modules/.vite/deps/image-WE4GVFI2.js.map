{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/fun.tobi/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,UklGRjIFAABXRUJQVlA4ICYFAABQHQCdASqAAIAAPm0wlkgkIqIhJxEq4IANiWIA1tzIBy/Zec1avv05aUzHY/+k+4Dta/cB7gH5Aeir+kvvb8zX6x/sB7sHpT+yX4AP2q62n0IPLY/bv4Uv2m/cP2g9WP/OzbvoE7JZakO59KanWxw40Fzi4gRl6d7VMrcMh/ogjZL85oCOn1bdq98mYAhGVf1z+HJgHsStyRihK2mNzu29sb1Ml01H4mruVTn9L4IsnhXPkcFHbu59dJV1CYKcAuKZcLj2EISvpDcmSbbSESZ9AzldEEhqIcIR3y8IW7HJU3Y22V6fe15HedP6kvxDQWSKpAAA/vnSyyyhBo8KwdKhbEjVL8GEEpX1nj9pV3tYU1OKB/2/DV/L/c+do9/BjcbnFq/5WNFjNy2pOr0oKcRlmC5DCUtQgjzlkvOXG+YUkJg9LHyF53IY9zEYvXTP0qSZbZxQJHO1jkSkGa2odmVOzFmisIPdZ7W8bF6/WA64XUBKe6oSMK11azF1HbftthoXHZm4p9SAlwz775kWiYzDNF3ZUugf65wdqFvxXBLL3k8ulr4hRNqNVMx5zxyNaF25brM4Uc7ySd28wM5ThoIt+/E5UxGqxSFLmETppVaN9uq+CMDCdY2nk3s5yQo/HBhv3RwJp6Z0/sL9u9fm/UKicDrnAZ8iSWUWiroF4EjDiZu1S5F1EHf+6xzvOxd7vc4+45ecYO5M+J7ORypgvfZ3z5VdDjQt517ZRAfU6gBZeR/M/MX2f7CbJ4ywf5ppGi9GXMS7lZA7I6zN9M2FX/D5kF71/f/2gfdJvcdL6y5mYfSOD9SbcFoyNKOXC9z6euHjxC80cmK7K1tWhecDZ+/I5p9TkXf72883OEnd6IEIGvIyU5LhsG5IshizOBqvEboDi48IyHY49ZfEt9/N+VN+kDHFBGT1cQGLdnLkxP/P4cAQ8fxGYGEVHuUDxyxjaH5cm1dsqpnnQv9cnoGebQf9apon4CH703V4lfo+32b8ReHvmFvB4zZJgm9gnOSbL6mYVVbXEgtF7d4Ijg0nv3GEWk8z3XDv7vxBlNFR/owPXE6DTFimwqlui1/v+2abC006Bgxy+CsiAH2oZnN+hHxxF2OaoYxMhHhJxF2ei2d3oPz/mdJICLRotrn1IsN9ZTtm5JwKqbZ/UAjFUmXqY3ApWx1vZz3zcqHOEm1BiutIaakFFHGjmCb/1OzJeMIr0VspvWAfR/77RMU6TFUlG6BN4vWThI/2eQHUruQzAk7/Bf5yZqUv4nADdpONqhFc6kUOPOXO/d1FFJmUGSB+Aa+gUAuPRyftUdNpT1Uq4RMZOaFlk6xH+71w3lGASPFIkZl0X04doyQloE3OpJoGfKRlbbGu57fyf5GhIU3QI6W5BFP5LyUhQcGXKML79d6rPDQUrdjS6aoy/nJi+qRYX1Sz/g5QNbyxI0cQu3mkazYnHsxggIM+k2WubikGJA/m68ndAau+z5okIpRaZ3KP+XO5lVifDpoM03uJlTWLeDi2yrLqFhK9H07Mezc0zcFIgmChPIssuUZAPBP4PZIAaLLOt//rrTC5P2L74PSlu4Oe1Q58m5nqLtMYbqFUHbdeP9YbKWKhtB8Z3AdclJ0mvrIefsI4qMrztoUzmFZL8SbmCDG84wdhYX36D4fA+/Bhl0vjbR+bAmUMiXEmkxJn7/UvsvOLStZNG8Qvoj4U58KpdbKRgGSjUi5zmx0nXRZZtoM5VTvNTmXysw0JRhf5nvkOeEEcAAAA\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}