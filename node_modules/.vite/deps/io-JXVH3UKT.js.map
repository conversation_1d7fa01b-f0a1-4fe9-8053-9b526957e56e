{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.yowallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.yowallet\",\n  name: \"<PERSON><PERSON><PERSON><PERSON>\",\n  homepage: \"https://yowallet.io\",\n  image_id: \"750079a0-6372-4e32-d1af-fe8ec2bbe400\",\n  app: {\n    browser: \"https://yowallet.io\",\n    ios: \"https://apps.apple.com/us/app/yowallet/id6474118944\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.yowallet.app&hl=en\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"yo-wallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}