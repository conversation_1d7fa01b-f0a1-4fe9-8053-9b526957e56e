{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.kigo/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.kigo\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://kigo.io\",\n  image_id: \"ad83d869-de11-4685-2a24-d3ce93a86400\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/kigo-digital/id6449599872\",\n    android: \"https://play.google.com/store/apps/details?id=com.augeo.kigo\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"kigo-mobile-app://\",\n    universal: \"https://kigo-digital.app.link/\",\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}