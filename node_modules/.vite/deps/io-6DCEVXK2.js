import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/io.zelcore/index.js
var wallet = {
  id: "io.zelcore",
  name: "Zelcore",
  homepage: "https://zelcore.io",
  image_id: "1b9e652e-1667-425a-f828-707bf9b05400",
  app: {
    browser: null,
    ios: "https://apps.apple.com/us/app/zelcore/id1436296839",
    android: "https://play.google.com/store/apps/details?id=com.zelcash.zelcore",
    mac: null,
    windows: null,
    linux: "https://zelcore.io/downloads/",
    chrome: null,
    firefox: null,
    safari: null,
    edge: null,
    opera: null
  },
  rdns: null,
  mobile: {
    native: "zel://",
    universal: null
  },
  desktop: {
    native: "zel://",
    universal: null
  }
};
export {
  wallet
};
//# sourceMappingURL=io-6DCEVXK2.js.map
