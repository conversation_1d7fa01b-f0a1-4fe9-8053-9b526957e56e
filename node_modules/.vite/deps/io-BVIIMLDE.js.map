{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.huddln/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.huddln\",\n  name: \"<PERSON>dd<PERSON>\",\n  homepage: \"https://www.huddln.io\",\n  image_id: \"7ba1571c-10c4-4284-b438-04dac27cb700\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/huddln-nft-social-network/id1503825604\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.huddln&hl=en_US&gl=US\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"huddln://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}