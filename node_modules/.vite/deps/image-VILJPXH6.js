import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/__generated__/wallet/com.moongate.one/image.js
var image = "data:image/webp;base64,UklGRkAFAABXRUJQVlA4IDQFAAAQHACdASqAAIAAPm0ylUckIyIhJpO76IANiWkIcAAsYHwp2Gf1rlcJW5h37K/s/7NxA7xL+Yf5r8hODFAB9WfRv9V8ytIpMu8e/077CPld+yfygDh3q3hgztU4x4Ua/dAkfUlhbvvOifpX9Yt5ddEhQgurDsyiATvkW5EgV7SUKN4UEj+jbUU2mLVyh+6oOeOVIwAKU9sP3z8NTS4f5PYJwFcYjpGatpJ2MNU9a9T8f2R6MWJIdoxdMD2dpcRED4BPkA+/Efliw2Gb+ErjaQvW210CyNO1rpe2BTMo7FHnijkUK3+5fdRmCAD+1croW49Oc8w3d+SMHiXmNBup4jekQg9IajiD1mxb7D/tuyuj86gVOWrONndUUX1DtL/GkLrgzrNbpTMEU14i6xvxS8H/Z9TrRDLA3KUsbeCMxhsmbEkO49W4T6w/x4Y81O162qsNcjTpcCleT9mrIY+vs325KwGH4Ju8LM7UwNVzqwG6aqBnIWq7/ML3tupdLg/BukWktG8f6SMIVwWdxrpNZihwC86U8p475Wv6GRqKtGoKg7G7SJG8+0ZktRNVoTDIjRUwQPp6ba5cKL0j2tQmvuyjGvCrl8rDoJ376QMdEgSs1NYNaXAsN0g6i8eKLLZRjHwywUIzBrQCzvD6Dtn/XELNCJBc4YBzaNiKUiqTA6bGqWHwK823vok1P8JEy8cwfVmh8Mm4TKj/ORdmix4jCFieC938T4GKLvNEYlINSDKzWvDMbMbwCYxGQlDeKikv/PhZZ+GMY903Y2NcNhtlCBFVfO0AmYoCAmNN4xKT6JYH2hXlqrkmN6Y4rdPqtH9jrdbuImXxb6AhscABy5td6eWjNn8MoYq3rVfSlpYF3tERuhypE+AH2wzBUTOA/5/9tmAT37GjC6v65rMFr0tgjgRK22gHka4f/0AlikNlvQQ3EfVBqllHNt2d2rkWo7K4r6tLWzAlQ/of3Siu7wViiPO2cZonnlSUS0qCx1i1EFxlcWIMkZw0hvRe8P+sROjpPtXdZZNd0wIcBFkqyr5w/QFYnEDy6+Qp/cXofu4+2J5OvSZT/k/QyxB/dLNCijTZtOGmExB5KsCj752j7B1Izrt8w7/GaDnG1AOOUnCev2fB1ncAUOGjZuaqUQE9Hk7CW9YcZ+esZL+yv7SsYflrJmhK5W6EvEBs24rHUebJSx1Rct9lxQKHWMz7D4fOpzXIoJiIMa7tmQXDOTRH7k/HUX47coa9RNHwbWrXP6cdsQ9gvkTk/g5MMM4I322bKvVHwnP+/h8Szpbcuct8k92KRG5TbqtPjmQexvv32KbmjWLgn5EPCjN1VjHDJTyL6598Gib222R71/QFKwWAFsbIkG/I+2fopcE8R031f5qP2cysAb1wJm1PmM75va0oa1uZ7gt1i+5QhABFFZD+hkUD5c7I2PjzTgkZQLJSzUdPENOfni6ELna9pfkLkegd0ZAfQLIK0ptIMgj2+bILIbR7GxfkjFa8l/rgxDK14O/nnDcjFvtAwEpUt9aqk5onS3lzf5oemg5Vp8+7XEUxjrDiWPjzTvyUVWbrqVt7TgwRua9Oadn/qDD/SD8Q500PT8Vl7FdGCMzYDMLmYi/U9/ba4k3v270ZpNHdNlLehKh+jX9xkTEFc/J6FZiXBpR15dlPnOudYOs3vKiXx6PS0FoniYb5GC0HpM4La/Vl0k+gvcKaTDUdDL6+VZJzhIgJBXVc1P3K9RLiQTvkB89xwbJnnRAuXVKbdlYHTsRq86IFtRXk+VFIAAA=";
var image_default = image;
export {
  image_default as default
};
//# sourceMappingURL=image-VILJPXH6.js.map
