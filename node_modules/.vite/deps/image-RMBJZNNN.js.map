{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/xyz.coca/image.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nconst image =\n  \"data:image/webp;base64,UklGRv4EAABXRUJQVlA4WAoAAAAQAAAAfwAAfwAAQUxQSDUAAAABD9D/iAgoaiRJ2d1DGSflpJ13ZvxE9F9MmrAAfoYADx2UB5bIZAM2EbgyLBG4RXru7/wQAABWUDggogQAANAcAJ0BKoAAgAA+bTaWR6QjIiEpm8hYgA2JYgcQVIPhj9m/IDT7fhL7SFc/nX4I9QHjNy13jvu12edYzzAP4B/IPw77CnmA8/D0cf4f1AP8r1G3oAeWt7Hf7v/s77DDyA6HOwGUEF/5NCoHt9fYkuBMV5WPI9XTSPxK+jv6i49Cn2Bz99gEW/bFY9XxV/o5tjsoh7R2UwLmeJ7i920JTQFVpMd1KZWC7GulzDfQCjNFyZYC2xet0RnO9zfPnjYAwKeb2cEzvqyn3uIq8baL0mEOaLcRKfsrJia1K+GTw0JDnKs2tnlIvCl7/jEcYxUAAP7eQEkLDr9Sz+pZWG50QLG5VvPQ2cwo5AQOnIIvzvkrVaJQBqfQ7I4DdHlq7b2eD3RL5p4NtrjsBUYP6HvYEx9Bc/UI/IF7PbMCHhp+9ZcePC4Ah8S9OQl76FezrUkaHq6F8mk4L2MvbJTYFMV9WCwKkUjdHCg9rWv6ZxmZglzDi8ktche9+1LGqNmLkwFPlg9kGW3PwBZMgPpAb52S7TgVoM0CQA8s8H9MgV1495oICaV5hkSZTOOxVY5VJPq5nRBG0QD0Jv76nTf5VrqAVCQ9p+rXeB9OJztagfWGQJiXG1rNFthR3N7/Y35HOWz2ycDv3P9jcpeTVesD/mPg8rr3PdQFe1bdaxT1jGA6fhJXOam+Z/DWVT0ivJpJYk0JE+EU3w7RY6X7mRt8VcBlE8fKqKjURXj8Xk2H+/+/lpcSc7vZ2asAuJdcOgOhMlXD5HbuLsYO7Iel8NAD7l/a7ZNI7dPI31jAPHFecz5n1lBYTKtfT6uUtJJtZQhoHpOZ3lrCR8rdTomkpxi4rncU6+6nNYYLLv6MXOgMcALPNclUUzkFwtBu6RySuomYG52VM0Ht2vcV+2plEQCE4jyJ6QDvLqVLFaiXva+egFklzxVnIPFK12jFnQfVMwK6xGWob/aTaguMZBlaFkqPx06hyIS4wmBq39MSzFYdhytpzNhkBDtZ2a6KNml1+4ySVJFQSTPn+qprX/C0juTn3oGKO11s3lC+8zcXA3ux8lh6eRcMEGJ0GPTsbN2hsbWmVj+osUa9ENtqDjN/jJ+1TEN3oSfvRW+NbYuy2Y5okosEjw4si4zdvn4ib194WLxTFwQbZ7E+MVfNM2rdgX5XarLx6x+vqb1Q/DBUQkVelTOhpZOjdtizKLp+ghikvLHRRj5UhViOslG2iNxBteYws2ATErQmpICq5RBpEX+4l8o4EQ2oDjd51eHM5DaDRG2YgDgUL+shFDyUbSUX3/mgjkGdvFKuYhQnbSSi0NX3Rf8Vykzv8fq4ewhHH+Zmjr8DCOr3fyveIdYP+kOw5WrnpPKi7uozl87FPhBwcOlmlSV2FiRa18OHQO/iiwDL0yqrKujcPZPDbgFmkWPe6RmNMoqRoQeFZHwDsx8KyTFU9ZpzhMbwV63wZEyBO2EfUN/CDOT9JK6CXeglGbkskLX4eO+9q6+TZD03TS6Nzfou5B4i85wydAC77LMYH4fEMlhwklrWhz0I9EAhJrPpa/mKo7CZtU+gcTLAwvDnRa4xBMMIlyAAAAA=\";\n\nexport default image;\n"], "mappings": ";;;AAGA,IAAM,QACJ;AAEF,IAAA,gBAAe;", "names": []}