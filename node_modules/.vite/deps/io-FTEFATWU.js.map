{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.clingon/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.clingon\",\n  name: \"Cling Wallet\",\n  homepage: \"https://clingon.io\",\n  image_id: \"2d8006c3-852b-458a-d6b0-916c5ba76800\",\n  app: {\n    browser:\n      \"https://chrome.google.com/webstore/detail/cling-wallet/kppgpfphbmbcgeglphjnhnhibonmebkn?hl=ko\",\n    ios: \"https://apps.apple.com/us/app/cling-wallet/id6443952504\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.carrieverse.cling.wallet&hl=en_US&gl=US\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"cling://\",\n    universal: \"https://cling.carrieverse.com/apple-app-site-association\",\n  },\n  desktop: {\n    native: null,\n    universal:\n      \"https://chrome.google.com/webstore/detail/cling-wallet/kppgpfphbmbcgeglphjnhnhibonmebkn?hl=ko\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SACE;IACF,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WACE;;;", "names": []}