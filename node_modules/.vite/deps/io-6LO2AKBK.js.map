{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.zelus/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.zelus\",\n  name: \"Zelus Wallet\",\n  homepage: \"https://zelus.io\",\n  image_id: \"a173eba6-05b4-43f4-0df6-400563637b00\",\n  app: {\n    browser: \"https://rollingloud.bridge.zelus.io\",\n    ios: \"https://apps.apple.com/us/app/zelus/id1588430343\",\n    android: \"https://play.google.com/store/apps/details?id=com.zelus.wallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"zeluswallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: \"https://rollingloud.bridge.zelus.io\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}