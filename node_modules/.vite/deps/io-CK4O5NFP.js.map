{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.cypherhq/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.cypherhq\",\n  name: \"Cypher Wallet\",\n  homepage: \"https://cypherhq.io\",\n  image_id: \"7bce0965-a4cc-4aad-6217-009d51017500\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/cypherd-wallet/id1604120414\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.cypherd.androidwallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome:\n      \"https://chrome.google.com/webstore/detail/cypher-wallet/niiaamnmgebpeejeemoifgdndgeaekhe\",\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"cypherwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"cypherwallet://\",\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QACE;IACF,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}