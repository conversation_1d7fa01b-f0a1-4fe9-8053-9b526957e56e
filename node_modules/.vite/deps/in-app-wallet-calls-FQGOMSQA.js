import {
  sendAndConfirmTransaction,
  sendBatchTransaction
} from "./chunk-KIS5WZSA.js";
import {
  eth_getTransactionReceipt
} from "./chunk-J7DXO4GY.js";
import "./chunk-QWTK625L.js";
import "./chunk-JK36SS76.js";
import "./chunk-HFJPNBPY.js";
import "./chunk-HGMV3JDR.js";
import "./chunk-E4AMV5H3.js";
import "./chunk-WLZN2VO2.js";
import "./chunk-HKVYRBWW.js";
import "./chunk-HAADYJEF.js";
import "./chunk-LVUFVX5C.js";
import "./chunk-UEKVYVRB.js";
import "./chunk-IHMGPG6V.js";
import "./chunk-MZGV4VDW.js";
import "./chunk-RPGMYTER.js";
import "./chunk-64KT6ODC.js";
import {
  randomBytesHex
} from "./chunk-EP3M7YWL.js";
import {
  getRpcClient
} from "./chunk-NTKAF5LO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-E4AXWHD7.js";
import "./chunk-H7Z32RTP.js";
import "./chunk-YXD4WFHV.js";
import "./chunk-26FWGFQH.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-KQKMGIQ6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-RJUQUX6Y.js";
import {
  LruMap
} from "./chunk-PPP72TBL.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/in-app/core/eip5972/in-app-wallet-calls.js
var bundlesToTransactions = new LruMap(1e3);
async function inAppWalletSendCalls(args) {
  const { account, calls } = args;
  const hashes = [];
  const id = randomBytesHex(65);
  bundlesToTransactions.set(id, hashes);
  if (account.sendBatchTransaction) {
    const receipt = await sendBatchTransaction({
      account,
      transactions: calls
    });
    hashes.push(receipt.transactionHash);
    bundlesToTransactions.set(id, hashes);
  } else {
    for (const tx of calls) {
      const receipt = await sendAndConfirmTransaction({
        account,
        transaction: tx
      });
      hashes.push(receipt.transactionHash);
      bundlesToTransactions.set(id, hashes);
    }
  }
  return id;
}
async function inAppWalletGetCallsStatus(args) {
  const { wallet, client, id } = args;
  const chain = wallet.getChain();
  if (!chain) {
    throw new Error("Failed to get calls status, no active chain found");
  }
  const bundle = bundlesToTransactions.get(id);
  if (!bundle) {
    throw new Error("Failed to get calls status, unknown bundle id");
  }
  const request = getRpcClient({ client, chain });
  let status = "success";
  const receipts = await Promise.all(bundle.map((hash) => eth_getTransactionReceipt(request, { hash }).then((receipt) => ({
    logs: receipt.logs.map((l) => ({
      address: l.address,
      data: l.data,
      topics: l.topics
    })),
    status: receipt.status,
    blockHash: receipt.blockHash,
    blockNumber: receipt.blockNumber,
    gasUsed: receipt.gasUsed,
    transactionHash: receipt.transactionHash
  })).catch(() => {
    status = "pending";
    return null;
  })));
  return {
    status,
    statusCode: 200,
    atomic: false,
    chainId: chain.id,
    id,
    version: "2.0.0",
    receipts: receipts.filter((r) => r !== null)
  };
}
export {
  inAppWalletGetCallsStatus,
  inAppWalletSendCalls
};
//# sourceMappingURL=in-app-wallet-calls-FQGOMSQA.js.map
