{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.ethos/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.ethos\",\n  name: \"Ethos Self-Custody Vault\",\n  homepage: \"https://www.ethos.io/\",\n  image_id: \"8bc7fb62-6f6b-4473-2e4a-5691a646fc00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/us/app/ethos-self-custody-vault/id6450948705\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.ethos2.prod&hl=en_US&gl=US&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"myapp://home\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}