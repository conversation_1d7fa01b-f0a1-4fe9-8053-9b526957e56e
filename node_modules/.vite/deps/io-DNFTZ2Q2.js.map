{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.ancrypto/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.ancrypto\",\n  name: \"Ancrypt<PERSON>\",\n  homepage: \"https://www.ancrypto.io/\",\n  image_id: \"8dee1c33-b277-4a5a-5ddd-5e70fd9d1800\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/in/app/ancrypto/id1660898349\",\n    android: \"https://play.google.com/store/apps/details?id=com.ancryptoWallet\",\n    mac: \"\",\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"ancrypto://app\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}