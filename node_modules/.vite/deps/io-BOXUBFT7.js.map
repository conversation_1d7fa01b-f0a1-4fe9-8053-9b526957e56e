{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.guardiianwallet/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.guardiianwallet\",\n  name: \"GUARDIIAN Wallet\",\n  homepage: \"https://guardiianwallet.io/\",\n  image_id: \"4f095c1d-8a31-4af8-ab58-57e82a398e00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/pk/app/guardiian-wallet/id6478384338\",\n    android: \"https://play.google.com/store/apps/details?id=com.gardianwallet\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"guardiianwallet://\",\n    universal: null,\n  },\n  desktop: {\n    native: null,\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}