{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.miraiapp/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.miraiapp\",\n  name: \"<PERSON><PERSON>\",\n  homepage: \"https://miraiapp.io\",\n  image_id: \"0c8022b0-d5a3-4561-64d5-a3e60d1ed500\",\n  app: {\n    browser: \"https://miraiapp.io\",\n    ios: \"https://apps.apple.com/vn/app/mirai-app-mpc-powered-wallet/id6472134236\",\n    android: \"https://play.google.com/store/apps/details?id=co.mirailabs.app\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"miraiapp://\",\n    universal: \"https://go.miraiapp.io\",\n  },\n  desktop: {\n    native: null,\n    universal: \"https://miraiapp.io\",\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SAAS;IACT,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}