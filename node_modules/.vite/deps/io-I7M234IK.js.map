{"version": 3, "sources": ["../../thirdweb/src/wallets/__generated__/wallet/io.hyperpay/index.ts"], "sourcesContent": ["// This file is auto-generated by the `scripts/wallets/generate.ts` script.\n// Do not modify this file manually.\n\nexport const wallet = {\n  id: \"io.hyperpay\",\n  name: \"HyperPay\",\n  homepage: \"https://www.hyperpay.io/\",\n  image_id: \"44abbf25-f8c4-4d04-0ce7-a695e00d8e00\",\n  app: {\n    browser: null,\n    ios: \"https://apps.apple.com/ae/app/hyperpay-bitcoin-crypto-wallet/id1354755812\",\n    android:\n      \"https://play.google.com/store/apps/details?id=com.legendwd.hyperpayW&hl&pli=1\",\n    mac: null,\n    windows: null,\n    linux: null,\n    chrome: null,\n    firefox: null,\n    safari: null,\n    edge: null,\n    opera: null,\n  },\n  rdns: null,\n  mobile: {\n    native: \"hyperPay://\",\n    universal: null,\n  },\n  desktop: {\n    native: \"hyperPay://\",\n    universal: null,\n  },\n} as const;\n"], "mappings": ";;;AAGO,IAAM,SAAS;EACpB,IAAI;EACJ,MAAM;EACN,UAAU;EACV,UAAU;EACV,KAAK;IACH,SAAS;IACT,KAAK;IACL,SACE;IACF,KAAK;IACL,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;;EAET,MAAM;EACN,QAAQ;IACN,QAAQ;IACR,WAAW;;EAEb,SAAS;IACP,QAAQ;IACR,WAAW;;;", "names": []}